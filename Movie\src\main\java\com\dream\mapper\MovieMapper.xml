<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dream.mapper.MovieMapper" >
  <resultMap id="BaseResultMap" type="com.dream.po.Movie" >
    <id column="movieid" property="movieid" jdbcType="INTEGER" />
    <result column="moviename" property="moviename" jdbcType="VARCHAR" />
    <result column="showyear" property="showyear" jdbcType="TIMESTAMP" />
    <result column="nation" property="nation" jdbcType="VARCHAR" />
    <result column="director" property="director" jdbcType="VARCHAR" />
    <result column="leadactors" property="leadactors" jdbcType="VARCHAR" />
    <result column="screenwriter" property="screenwriter" jdbcType="VARCHAR" />
    <result column="picture" property="picture" jdbcType="VARCHAR" />
    <result column="averating" property="averating" jdbcType="DOUBLE" />
    <result column="numrating" property="numrating" jdbcType="INTEGER" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="typelist" property="typelist" jdbcType="VARCHAR" />
    <result column="backpost" property="backpost" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    movieid, moviename, showyear, nation, director, leadactors, screenwriter, picture,averating,numrating,description,typelist,backpost
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.dream.po.MovieExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from movie LIMIT 20
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from movie
    where movieid = #{movieid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from movie
    where movieid = #{movieid,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dream.po.MovieExample" >
    delete from movie
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dream.po.Movie" >
    insert into movie (movieid, moviename, showyear, 
      nation, director, leadactors, 
      screenwriter,picture,averating,numrating,description)
    values (#{movieid,jdbcType=INTEGER}, #{moviename,jdbcType=VARCHAR}, #{showyear,jdbcType=TIMESTAMP}, 
      #{nation,jdbcType=VARCHAR}, #{director,jdbcType=VARCHAR}, #{leadactors,jdbcType=VARCHAR}, 
      #{screenwriter,jdbcType=VARCHAR}, #{picture,jdbcType=VARCHAR},#{averating,jdbcType=DOUBLE},#{numrating,jdbcType=INTEGER},#{description,jdbcType=VARCHAR},#{typelist,jdbcType=VARCHAR},#{backpost,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.dream.po.Movie" >
    insert into movie
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="movieid != null" >
        movieid,
      </if>
      <if test="moviename != null" >
        moviename,
      </if>
      <if test="showyear != null" >
        showyear,
      </if>
      <if test="nation != null" >
        nation,
      </if>
      <if test="director != null" >
        director,
      </if>
      <if test="leadactors != null" >
        leadactors,
      </if>
      <if test="screenwriter != null" >
        screenwriter,
      </if>
      <if test="picture != null" >
        picture,
      </if>
      <if test="averating != null" >
        averating,
      </if>
      <if test="numrating != null" >
        numrating,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="typelist != null" >
        typelist,
      </if>
      <if test="backpost != null" >
        backpost,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="movieid != null" >
        #{movieid,jdbcType=INTEGER},
      </if>
      <if test="moviename != null" >
        #{moviename,jdbcType=VARCHAR},
      </if>
      <if test="showyear != null" >
        #{showyear,jdbcType=TIMESTAMP},
      </if>
      <if test="nation != null" >
        #{nation,jdbcType=VARCHAR},
      </if>
      <if test="director != null" >
        #{director,jdbcType=VARCHAR},
      </if>
      <if test="leadactors != null" >
        #{leadactors,jdbcType=VARCHAR},
      </if>
      <if test="screenwriter != null" >
        #{screenwriter,jdbcType=VARCHAR},
      </if>
      <if test="picture != null" >
        #{picture,jdbcType=VARCHAR},
      </if>
      <if test="averating != null" >
        #{averating,jdbcType=DOUBLE},
      </if>
      <if test="numrating != null" >
        #{numrating,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="typelist != null" >
        #{typelist,jdbcType=VARCHAR},
      </if>
      <if test="backpost != null" >
        #{backcpost,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dream.po.MovieExample" resultType="java.lang.Integer" >
    select count(*) from movie
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update movie
    <set >
      <if test="record.movieid != null" >
        movieid = #{record.movieid,jdbcType=INTEGER},
      </if>
      <if test="record.moviename != null" >
        moviename = #{record.moviename,jdbcType=VARCHAR},
      </if>
      <if test="record.showyear != null" >
        showyear = #{record.showyear,jdbcType=TIMESTAMP},
      </if>
      <if test="record.nation != null" >
        nation = #{record.nation,jdbcType=VARCHAR},
      </if>
      <if test="record.director != null" >
        director = #{record.director,jdbcType=VARCHAR},
      </if>
      <if test="record.leadactors != null" >
        leadactors = #{record.leadactors,jdbcType=VARCHAR},
      </if>
      <if test="record.screenwriter != null" >
        screenwriter = #{record.screenwriter,jdbcType=VARCHAR},
      </if>
      <if test="record.picture != null" >
        picture = #{record.picture,jdbcType=VARCHAR},
      </if>
      <if test="record.averating != null" >
        averating = #{record.averating,jdbcType=DOUBLE},
      </if>
      <if test="record.numrating != null" >
        numrating = #{record.numrating,jdbcType=INTEGER},
      </if>
      <if test="record.description != null" >
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.typelist != null" >
        typelist = #{record.typelist,jdbcType=VARCHAR},
      </if>
      <if test="record.backpost != null" >
        backpost = #{record.backpost,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update movie
    set movieid = #{record.movieid,jdbcType=INTEGER},
      moviename = #{record.moviename,jdbcType=VARCHAR},
      showyear = #{record.showyear,jdbcType=TIMESTAMP},
      nation = #{record.nation,jdbcType=VARCHAR},
      director = #{record.director,jdbcType=VARCHAR},
      leadactors = #{record.leadactors,jdbcType=VARCHAR},
      screenwriter = #{record.screenwriter,jdbcType=VARCHAR},
      picture = #{record.picture,jdbcType=VARCHAR},
      averating = #{record.averating,jdbcType=DOUBLE},
      numrating = #{record.numrating,jdbcType=INTEGER},
      description = #{record.description,jdbcType=VARCHAR},
      typelist = #{record.typelist,jdbcType=VARCHAR},
      backpost = #{record.backpost,jdbcType=VARCHAR},
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dream.po.Movie" >
    update movie
    <set >
      <if test="moviename != null" >
        moviename = #{moviename,jdbcType=VARCHAR},
      </if>
      <if test="showyear != null" >
        showyear = #{showyear,jdbcType=TIMESTAMP},
      </if>
      <if test="nation != null" >
        nation = #{nation,jdbcType=VARCHAR},
      </if>
      <if test="director != null" >
        director = #{director,jdbcType=VARCHAR},
      </if>
      <if test="leadactors != null" >
        leadactors = #{leadactors,jdbcType=VARCHAR},
      </if>
      <if test="screenwriter != null" >
        screenwriter = #{screenwriter,jdbcType=VARCHAR},
      </if>
      <if test="picture != null" >
        picture = #{picture,jdbcType=VARCHAR},
      </if>
      <if test="averating != null" >
        averating = #{averating,jdbcType=DOUBLE},
      </if>
      <if test="numrating != null" >
        numrating = #{numrating,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="typelist != null" >
        typelist = #{typelist,jdbcType=VARCHAR},
      </if>
      <if test="backpost != null" >
        backpost = #{backpost,jdbcType=VARCHAR},
      </if>
    </set>
    where movieid = #{movieid,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dream.po.Movie" >
    update movie
    set moviename = #{moviename,jdbcType=VARCHAR},
      showyear = #{showyear,jdbcType=TIMESTAMP},
      nation = #{nation,jdbcType=VARCHAR},
      director = #{director,jdbcType=VARCHAR},
      leadactors = #{leadactors,jdbcType=VARCHAR},
      screenwriter = #{screenwriter,jdbcType=VARCHAR},
      picture = #{picture,jdbcType=VARCHAR},
      averating = #{averating,jdbcType=DOUBLE},
      numrating = #{numrating,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      typelist = #{record.typelist,jdbcType=VARCHAR},
      backpost = #{record.backpost,jdbcType=VARCHAR},
      where movieid = #{movieid,jdbcType=INTEGER}
  </update>
  <select id="selectBycategory" parameterType="com.dream.po.Selectquery" resultMap="BaseResultMap">
    <if test="category == 0">
      select * from movie order by ${sort} desc
      LIMIT #{molimit,jdbcType=INTEGER},20;
    </if>
    <if test="category != 0">
    select * from movie m, moviecategory mc where m.movieid=mc.movieid and mc.categoryid = #{category,jdbcType=INTEGER} order by ${sort} desc
      LIMIT #{molimit,jdbcType=INTEGER},20;
    </if>
  </select>
<select id="SelectTopDefaultMovie" resultMap="BaseResultMap">
  SELECT  * FROM topdefaultmovies t,movie m WHERE t.movieid=m.movieid limit #{limit};
</select>
  <select id="selectByNameLike" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from movie
    <where>
      <if test="_parameter != null">
        moviename like "%"#{moviename}"%"
      </if>
    </where>
    LIMIT 10;
  </select>
</mapper>