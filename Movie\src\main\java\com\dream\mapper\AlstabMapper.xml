<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dream.mapper.AlstabMapper" >
  <resultMap id="BaseResultMap" type="com.dream.po.Alstab" >
    <result column="userId" property="userid" jdbcType="INTEGER" />
    <result column="movieId" property="movieid" jdbcType="INTEGER" />
    <result column="rating" property="rating" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    userId, movieId, rating
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.dream.po.AlstabExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from alstab
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.dream.po.AlstabExample" >
    delete from alstab
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dream.po.Alstab" >
    insert into alstab (userId, movieId, rating
      )
    values (#{userid,jdbcType=INTEGER}, #{movieid,jdbcType=INTEGER}, #{rating,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.dream.po.Alstab" >
    insert into alstab
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userid != null" >
        userId,
      </if>
      <if test="movieid != null" >
        movieId,
      </if>
      <if test="rating != null" >
        rating,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userid != null" >
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="movieid != null" >
        #{movieid,jdbcType=INTEGER},
      </if>
      <if test="rating != null" >
        #{rating,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dream.po.AlstabExample" resultType="java.lang.Integer" >
    select count(*) from alstab
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update alstab
    <set >
      <if test="record.userid != null" >
        userId = #{record.userid,jdbcType=INTEGER},
      </if>
      <if test="record.movieid != null" >
        movieId = #{record.movieid,jdbcType=INTEGER},
      </if>
      <if test="record.rating != null" >
        rating = #{record.rating,jdbcType=DOUBLE},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update alstab
    set userId = #{record.userid,jdbcType=INTEGER},
      movieId = #{record.movieid,jdbcType=INTEGER},
      rating = #{record.rating,jdbcType=DOUBLE}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectAlsByExampleStr" resultType="java.lang.Integer" parameterType="com.dream.po.AlstabExample" >
    select movieId from alstab
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause} DESC
    </if>
    AND rating >= 6.0
  </select>
</mapper>