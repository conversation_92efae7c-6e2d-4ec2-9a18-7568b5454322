<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dream.mapper.UserMapper" >
  <resultMap id="BaseResultMap" type="com.dream.po.User" >
    <id column="userid" property="userid" jdbcType="INTEGER" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="registertime" property="registertime" jdbcType="TIMESTAMP" />
    <result column="lastlogintime" property="lastlogintime" jdbcType="TIMESTAMP" />
    <result column="email" property="email" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    userid, username, password, registertime, lastlogintime, email
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.dream.po.UserExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from user
    where userid = #{userid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from user
    where userid = #{userid,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dream.po.UserExample" >
    delete from user
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dream.po.User"  useGeneratedKeys="true" keyProperty="userid">
    insert into user (userid, username, password,
    registertime, lastlogintime,email)
    values (#{userid,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
    #{registertime,jdbcType=TIMESTAMP}, #{lastlogintime,jdbcType=TIMESTAMP},#{email,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.dream.po.User" >
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userid != null" >
        userid,
      </if>
      <if test="username != null" >
        username,
      </if>
      <if test="password != null" >
        password,
      </if>
      <if test="registertime != null" >
        registertime,
      </if>
      <if test="lastlogintime != null" >
        lastlogintime,
      </if>
      <if test="email != null" >
        email,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userid != null" >
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="username != null" >
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="registertime != null" >
        #{registertime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastlogintime != null" >
        #{lastlogintime,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dream.po.UserExample" resultType="java.lang.Integer" >
    select count(*) from user
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update user
    <set >
      <if test="record.userid != null" >
        userid = #{record.userid,jdbcType=INTEGER},
      </if>
      <if test="record.username != null" >
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null" >
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.registertime != null" >
        registertime = #{record.registertime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastlogintime != null" >
        lastlogintime = #{record.lastlogintime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.email != null" >
        email = #{record.email,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update user
    set userid = #{record.userid,jdbcType=INTEGER},
    username = #{record.username,jdbcType=VARCHAR},
    password = #{record.password,jdbcType=VARCHAR},
    registertime = #{record.registertime,jdbcType=TIMESTAMP},
    lastlogintime = #{record.lastlogintime,jdbcType=TIMESTAMP}
    email = #{record.email,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dream.po.User" >
    update user
    <set >
      <if test="username != null" >
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="registertime != null" >
        registertime = #{registertime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastlogintime != null" >
        lastlogintime = #{lastlogintime,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null" >
        email = #{email,jdbcType=VARCHAR},
      </if>
    </set>
    where userid = #{userid,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dream.po.User" >
    update user
    set username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      registertime = #{registertime,jdbcType=TIMESTAMP},
      lastlogintime = #{lastlogintime,jdbcType=TIMESTAMP}
      email = #{lastlogintime,jdbcType=VARCHAR}
    where userid = #{userid,jdbcType=INTEGER}
  </update>
</mapper>