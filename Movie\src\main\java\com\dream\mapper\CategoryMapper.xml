<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dream.mapper.CategoryMapper" >
  <resultMap id="BaseResultMap" type="com.dream.po.Category" >
    <id column="categoryid" property="categoryid" jdbcType="INTEGER" />
    <result column="category" property="category" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    categoryid, category
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.dream.po.CategoryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from category
    where categoryid = #{categoryid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from category
    where categoryid = #{categoryid,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dream.po.CategoryExample" >
    delete from category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dream.po.Category" >
    insert into category (categoryid, category)
    values (#{categoryid,jdbcType=INTEGER}, #{category,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.dream.po.Category" >
    insert into category
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="categoryid != null" >
        categoryid,
      </if>
      <if test="category != null" >
        category,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="categoryid != null" >
        #{categoryid,jdbcType=INTEGER},
      </if>
      <if test="category != null" >
        #{category,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dream.po.CategoryExample" resultType="java.lang.Integer" >
    select count(*) from category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update category
    <set >
      <if test="record.categoryid != null" >
        categoryid = #{record.categoryid,jdbcType=INTEGER},
      </if>
      <if test="record.category != null" >
        category = #{record.category,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update category
    set categoryid = #{record.categoryid,jdbcType=INTEGER},
      category = #{record.category,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dream.po.Category" >
    update category
    <set >
      <if test="category != null" >
        category = #{category,jdbcType=VARCHAR},
      </if>
    </set>
    where categoryid = #{categoryid,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dream.po.Category" >
    update category
    set category = #{category,jdbcType=VARCHAR}
    where categoryid = #{categoryid,jdbcType=INTEGER}
  </update>
</mapper>