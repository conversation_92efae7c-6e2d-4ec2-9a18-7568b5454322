<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dream.mapper.TopdefaultmoviesMapper" >
  <resultMap id="BaseMovieResultMap" type="com.dream.po.Movie" >
    <id column="movieid" property="movieid" jdbcType="INTEGER" />
    <result column="moviename" property="moviename" jdbcType="VARCHAR" />
    <result column="showyear" property="showyear" jdbcType="TIMESTAMP" />
    <result column="nation" property="nation" jdbcType="VARCHAR" />
    <result column="director" property="director" jdbcType="VARCHAR" />
    <result column="leadactors" property="leadactors" jdbcType="VARCHAR" />
    <result column="screenwriter" property="screenwriter" jdbcType="VARCHAR" />
    <result column="picture" property="picture" jdbcType="VARCHAR" />
    <result column="averating" property="averating" jdbcType="DOUBLE" />
    <result column="numrating" property="numrating" jdbcType="INTEGER" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="typelist" property="typelist" jdbcType="VARCHAR" />
    <result column="backpost" property="backpost" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="BaseResultMap" type="com.dream.po.Topdefaultmovies" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="movieid" property="movieid" jdbcType="INTEGER" />
    <result column="moviename" property="moviename" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, movieid, moviename
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.dream.po.TopdefaultmoviesExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from topdefaultmovies
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from topdefaultmovies
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from topdefaultmovies
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dream.po.TopdefaultmoviesExample" >
    delete from topdefaultmovies
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dream.po.Topdefaultmovies" >
    insert into topdefaultmovies (id, movieid, moviename
      )
    values (#{id,jdbcType=INTEGER}, #{movieid,jdbcType=INTEGER}, #{moviename,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.dream.po.Topdefaultmovies" >
    insert into topdefaultmovies
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="movieid != null" >
        movieid,
      </if>
      <if test="moviename != null" >
        moviename,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="movieid != null" >
        #{movieid,jdbcType=INTEGER},
      </if>
      <if test="moviename != null" >
        #{moviename,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dream.po.TopdefaultmoviesExample" resultType="java.lang.Integer" >
    select count(*) from topdefaultmovies
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update topdefaultmovies
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.movieid != null" >
        movieid = #{record.movieid,jdbcType=INTEGER},
      </if>
      <if test="record.moviename != null" >
        moviename = #{record.moviename,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update topdefaultmovies
    set id = #{record.id,jdbcType=INTEGER},
      movieid = #{record.movieid,jdbcType=INTEGER},
      moviename = #{record.moviename,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dream.po.Topdefaultmovies" >
    update topdefaultmovies
    <set >
      <if test="movieid != null" >
        movieid = #{movieid,jdbcType=INTEGER},
      </if>
      <if test="moviename != null" >
        moviename = #{moviename,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dream.po.Topdefaultmovies" >
    update topdefaultmovies
    set movieid = #{movieid,jdbcType=INTEGER},
      moviename = #{moviename,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectRegDefaultMovie" resultMap="BaseMovieResultMap">
    SELECT  * FROM topdefaultmovies t,movie m WHERE t.movieid=m.movieid limit 10;
  </select>

</mapper>