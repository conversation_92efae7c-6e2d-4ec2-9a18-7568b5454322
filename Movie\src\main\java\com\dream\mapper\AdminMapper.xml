<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dream.mapper.AdminMapper" >
  <resultMap id="BaseResultMap" type="com.dream.po.Admin" >
    <id column="adminid" property="adminid" jdbcType="INTEGER" />
    <result column="adminname" property="adminname" jdbcType="VARCHAR" />
    <result column="adminpassword" property="adminpassword" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    adminid, adminname, adminpassword
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.dream.po.AdminExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from admin
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from admin
    where adminid = #{adminid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from admin
    where adminid = #{adminid,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dream.po.AdminExample" >
    delete from admin
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dream.po.Admin" >
    insert into admin (adminid, adminname, adminpassword
      )
    values (#{adminid,jdbcType=INTEGER}, #{adminname,jdbcType=VARCHAR}, #{adminpassword,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.dream.po.Admin" >
    insert into admin
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="adminid != null" >
        adminid,
      </if>
      <if test="adminname != null" >
        adminname,
      </if>
      <if test="adminpassword != null" >
        adminpassword,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="adminid != null" >
        #{adminid,jdbcType=INTEGER},
      </if>
      <if test="adminname != null" >
        #{adminname,jdbcType=VARCHAR},
      </if>
      <if test="adminpassword != null" >
        #{adminpassword,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dream.po.AdminExample" resultType="java.lang.Integer" >
    select count(*) from admin
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update admin
    <set >
      <if test="record.adminid != null" >
        adminid = #{record.adminid,jdbcType=INTEGER},
      </if>
      <if test="record.adminname != null" >
        adminname = #{record.adminname,jdbcType=VARCHAR},
      </if>
      <if test="record.adminpassword != null" >
        adminpassword = #{record.adminpassword,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update admin
    set adminid = #{record.adminid,jdbcType=INTEGER},
      adminname = #{record.adminname,jdbcType=VARCHAR},
      adminpassword = #{record.adminpassword,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dream.po.Admin" >
    update admin
    <set >
      <if test="adminname != null" >
        adminname = #{adminname,jdbcType=VARCHAR},
      </if>
      <if test="adminpassword != null" >
        adminpassword = #{adminpassword,jdbcType=VARCHAR},
      </if>
    </set>
    where adminid = #{adminid,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dream.po.Admin" >
    update admin
    set adminname = #{adminname,jdbcType=VARCHAR},
      adminpassword = #{adminpassword,jdbcType=VARCHAR}
    where adminid = #{adminid,jdbcType=INTEGER}
  </update>
</mapper>