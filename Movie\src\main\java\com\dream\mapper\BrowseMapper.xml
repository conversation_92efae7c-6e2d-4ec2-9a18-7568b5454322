<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dream.mapper.BrowseMapper" >
  <resultMap id="BaseResultMap" type="com.dream.po.Browse" >
    <id column="browseid" property="browseid" jdbcType="INTEGER" />
    <result column="userid" property="userid" jdbcType="INTEGER" />
    <result column="movieids" property="movieids" jdbcType="INTEGER" />
    <result column="browsetime" property="browsetime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    browseid, userid, movieids, browsetime
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.dream.po.BrowseExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from browse
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from browse
    where browseid = #{browseid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from browse
    where browseid = #{browseid,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dream.po.BrowseExample" >
    delete from browse
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dream.po.Browse" >
    insert into browse (browseid, userid, movieids, 
      browsetime)
    values (#{browseid,jdbcType=INTEGER}, #{userid,jdbcType=INTEGER}, #{movieids,jdbcType=INTEGER}, 
      #{browsetime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dream.po.Browse" >
    insert into browse
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="browseid != null" >
        browseid,
      </if>
      <if test="userid != null" >
        userid,
      </if>
      <if test="movieids != null" >
        movieids,
      </if>
      <if test="browsetime != null" >
        browsetime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="browseid != null" >
        #{browseid,jdbcType=INTEGER},
      </if>
      <if test="userid != null" >
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="movieids != null" >
        #{movieids,jdbcType=INTEGER},
      </if>
      <if test="browsetime != null" >
        #{browsetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dream.po.BrowseExample" resultType="java.lang.Integer" >
    select count(*) from browse
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update browse
    <set >
      <if test="record.browseid != null" >
        browseid = #{record.browseid,jdbcType=INTEGER},
      </if>
      <if test="record.userid != null" >
        userid = #{record.userid,jdbcType=INTEGER},
      </if>
      <if test="record.movieids != null" >
        movieids = #{record.movieids,jdbcType=INTEGER},
      </if>
      <if test="record.browsetime != null" >
        browsetime = #{record.browsetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update browse
    set browseid = #{record.browseid,jdbcType=INTEGER},
      userid = #{record.userid,jdbcType=INTEGER},
      movieids = #{record.movieids,jdbcType=INTEGER},
      browsetime = #{record.browsetime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dream.po.Browse" >
    update browse
    <set >
      <if test="userid != null" >
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="movieids != null" >
        movieids = #{movieids,jdbcType=INTEGER},
      </if>
      <if test="browsetime != null" >
        browsetime = #{browsetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where browseid = #{browseid,jdbcType=INTEGER}
  </update>

  <insert id="insertuserfavourtemovie" parameterType="com.dream.po.Selectquery">
    <if test="molimit==1">
      update browse set movieids=concat(movieids,#{sort}) where userid =#{category};
      INSERT INTO browse(userid, movieids,browsetime)
      SELECT #{category},#{sort},NULL
      FROM DUAL
      WHERE NOT EXISTS (select movieids from browse where userid =#{category});
    </if>
    <if test="molimit==0">
      update  browse  set  movieids=replace(movieids,#{sort},'') where userid =#{category};
    </if>
  </insert>
  <select id="booluserunlikedmovie"  resultType="java.lang.Integer">
        select count(*) from browse where  userid = #{0}   AND  movieids like "%,"#{1}"%.";
  </select>
</mapper>